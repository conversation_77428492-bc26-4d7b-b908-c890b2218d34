import axios from '@/services/axios'

export async function solicitarMentoria(paciente_id, observacao) {
    try {
        const response = await axios.post('/mentorias/solicitar', {
            paciente_id,
            observacao,
        });

        if (!response || !response.data || response.data.status !== 'success')
            return false

        return response.data.data

    } catch (error) {
        console.error('Erro ao solicitar mentoria:', error);
    }

    return false
}

export async function getMentorias() {
    try {
        const response = await axios.get('/mentorias');

        if (!response || !response.data || response.data.status !== 'success')
            return false

        return response.data.data

    } catch (error) {
        console.error('Erro ao buscar mentorias:', error);
    }

    return false
}

export async function getMentoria(id) {
    try {
        const response = await axios.get(`/mentorias/${id}`);

        if (!response || !response.data || response.data.status !== 'success')
            return false

        return response.data.data

    } catch (error) {
        console.error('Erro ao buscar mentoria:', error);
    }

    return false
}

export async function enviarMensagem(mentoria_id, mensagem) {
    try {
        const response = await axios.post('/mentorias/mensagem', {
            mentoria_id,
            mensagem,
        });

        if (!response || !response.data || response.data.status !== 'success')
            return false

        return response.data.data

    } catch (error) {
        console.error('Erro ao enviar mensagem:', error);
    }

    return false
}

export async function marcarMensagensComoLidas(mentoria_id) {
    try {
        const response = await axios.put(`/mentorias/${mentoria_id}/marcar-lidas`);

        if (!response || !response.data || response.data.status !== 'success')
            return false

        return response.data.data

    } catch (error) {
        console.error('Erro ao marcar mensagens como lidas:', error);
    }

    return false
}